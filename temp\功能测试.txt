CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_yfyy`(
    IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_pregnant_unit VARCHAR(10),
    IN p_pregnant VARCHAR(10)
)
    COMMENT '孕妇用药分析存储过程'
BEGIN
    DECLARE v_sda_id VARCHAR(20);
    DECLARE v_ywa_name VARCHAR(50);
    DECLARE v_yz INT DEFAULT 0;
    
    -- 异常处理
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 如果出现异常，继续执行
        BEGIN END;
    END;
    
    -- 获取药品名称
    SELECT DRUG_NAME INTO v_ywa_name
    FROM rms_itf_hos_drug 
    WHERE DRUG_CODE = p_yp_code
    LIMIT 1;
    
    -- 获取标准数据ID
    SELECT sda_id INTO v_sda_id
    FROM rms_t_byyydzb  
    WHERE akb020 = p_akb020 AND yp_code = p_yp_code
    LIMIT 1;
    
    -- 计算孕周天数
    SET v_yz = rms_fn_get_yz(p_pregnant_unit, p_pregnant);
    
    -- 如果有孕周信息，按孕周范围查询
    IF v_yz > 0 THEN
        INSERT INTO rms_t_pres_fx (
            Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
        )
        SELECT 
            p_Code,
            v_ywa_name AS ywa,
            '' AS ywb,
            '1' AS wtlvlcode,
            (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.bs) AS wtlvl,
            CASE 
                WHEN a.bs = '0' THEN 'RLT006'
                WHEN a.bs = '1' THEN 'RLT017'
            END AS wtcode,
            CASE 
                WHEN a.bs = '0' THEN 'RSQFNJJ'
                WHEN a.bs = '1' THEN 'RSQFNWT'
            END AS wtsp,
            CASE 
                WHEN a.bs = '0' THEN '特殊人群禁用'
                WHEN a.bs = '1' THEN '特殊人群慎用'
            END AS wtname,
            '妊娠用药' AS title,
            CONCAT('说明书提示：【', CAST(c.ym AS CHAR), '】', CAST(c.yfyy AS CHAR(500))) AS detail,
            0,
            '孕妇用药'
        FROM rms_t_sda_gestation a, rms_t_byyydzb b, rms_t_sda c
        WHERE a.sda_id = b.sda_id
        AND b.sda_id = c.ID
        AND b.akb020 = p_akb020
        AND b.yp_code = p_yp_code
        AND a.dayup <= v_yz
        AND a.daydown >= v_yz;
    ELSE
        -- 如果没有孕周信息，查询默认范围
        INSERT INTO rms_t_pres_fx (
            Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
        )
        SELECT 
            p_Code,
            v_ywa_name AS ywa,
            '' AS ywb,
            '1' AS wtlvlcode,
            (SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = a.bs) AS wtlvl,
            CASE 
                WHEN a.bs = '0' THEN 'RLT006'
                WHEN a.bs = '1' THEN 'RLT017'
            END AS wtcode,
            CASE 
                WHEN a.bs = '0' THEN 'RSQFNJJ'
                WHEN a.bs = '1' THEN 'RSQFNWT'
            END AS wtsp,
            CASE 
                WHEN a.bs = '0' THEN '特殊人群禁用'
                WHEN a.bs = '1' THEN '特殊人群慎用'
            END AS wtname,
            '妊娠用药' AS title,
            CONCAT('说明书提示：', CAST(c.ym AS CHAR), CAST(c.yfyy AS CHAR(500))) AS detail,
            0,
            '孕妇用药'
        FROM rms_t_sda_gestation a, rms_t_byyydzb b, rms_t_sda c
        WHERE a.sda_id = b.sda_id
        AND b.sda_id = c.ID
        AND b.akb020 = p_akb020
        AND b.yp_code = p_yp_code
        AND a.dayup <= -100
        AND a.daydown >= 1000;
    END IF;
    
END